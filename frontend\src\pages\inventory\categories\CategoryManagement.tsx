import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    Plus,
    Search,
    Edit,
    Trash2,
    MoreHorizontal,
    Package,
    CheckCircle,
    AlertCircle,
    <PERSON>older,
    Tag
} from "lucide-react"

// Mock data for categories
const mockCategories = [
    {
        id: 1,
        name: "Grains",
        description: "Rice, wheat, and other grain products",
        productCount: 15,
        status: "Active",
        createdAt: "2024-01-15"
    },
    {
        id: 2,
        name: "Spices",
        description: "All types of spices and seasonings",
        productCount: 28,
        status: "Active",
        createdAt: "2024-01-15"
    },
    {
        id: 3,
        name: "Dairy",
        description: "Milk, cheese, yogurt and dairy products",
        productCount: 12,
        status: "Active",
        createdAt: "2024-01-15"
    },
    {
        id: 4,
        name: "Instant Food",
        description: "Ready-to-cook and instant food items",
        productCount: 8,
        status: "Active",
        createdAt: "2024-01-16"
    },
    {
        id: 5,
        name: "Snacks",
        description: "Biscuits, chips, and snack items",
        productCount: 22,
        status: "Active",
        createdAt: "2024-01-16"
    },
    {
        id: 6,
        name: "Beverages",
        description: "Soft drinks, juices, and beverages",
        productCount: 0,
        status: "Inactive",
        createdAt: "2024-01-17"
    }
]

// Mock data for units
const mockUnits = [
    { id: 1, name: "kg", description: "Kilogram", productCount: 25, status: "Active" },
    { id: 2, name: "g", description: "Gram", productCount: 8, status: "Active" },
    { id: 3, name: "l", description: "Liter", productCount: 12, status: "Active" },
    { id: 4, name: "ml", description: "Milliliter", productCount: 15, status: "Active" },
    { id: 5, name: "pack", description: "Package/Pack", productCount: 18, status: "Active" },
    { id: 6, name: "piece", description: "Individual piece", productCount: 6, status: "Active" },
    { id: 7, name: "dozen", description: "12 pieces", productCount: 3, status: "Active" },
    { id: 8, name: "box", description: "Box/Carton", productCount: 0, status: "Inactive" }
]

export default function CategoryManagement() {
    const [activeTab, setActiveTab] = useState<"categories" | "units">("categories")
    const [categories, setCategories] = useState(mockCategories)
    const [units, setUnits] = useState(mockUnits)
    const [searchTerm, setSearchTerm] = useState("")
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [selectedItem, setSelectedItem] = useState<any>(null)
    const [showSuccess, setShowSuccess] = useState(false)
    const [successMessage, setSuccessMessage] = useState("")

    const [formData, setFormData] = useState({
        name: "",
        description: "",
        status: "Active"
    })

    const resetForm = () => {
        setFormData({
            name: "",
            description: "",
            status: "Active"
        })
    }

    const handleAdd = () => {
        const newItem = {
            id: Date.now(),
            ...formData,
            productCount: 0,
            createdAt: new Date().toISOString().split('T')[0]
        }

        if (activeTab === "categories") {
            setCategories([...categories, newItem])
            setSuccessMessage("Category added successfully!")
        } else {
            setUnits([...units, newItem])
            setSuccessMessage("Unit added successfully!")
        }

        setIsAddDialogOpen(false)
        resetForm()
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const handleEdit = () => {
        if (activeTab === "categories") {
            setCategories(categories.map(cat => 
                cat.id === selectedItem.id ? { ...cat, ...formData } : cat
            ))
            setSuccessMessage("Category updated successfully!")
        } else {
            setUnits(units.map(unit => 
                unit.id === selectedItem.id ? { ...unit, ...formData } : unit
            ))
            setSuccessMessage("Unit updated successfully!")
        }

        setIsEditDialogOpen(false)
        setSelectedItem(null)
        resetForm()
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const handleDelete = () => {
        if (activeTab === "categories") {
            setCategories(categories.filter(cat => cat.id !== selectedItem.id))
            setSuccessMessage("Category deleted successfully!")
        } else {
            setUnits(units.filter(unit => unit.id !== selectedItem.id))
            setSuccessMessage("Unit deleted successfully!")
        }

        setIsDeleteDialogOpen(false)
        setSelectedItem(null)
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const openEditDialog = (item: any) => {
        setSelectedItem(item)
        setFormData({
            name: item.name,
            description: item.description,
            status: item.status
        })
        setIsEditDialogOpen(true)
    }

    const openDeleteDialog = (item: any) => {
        setSelectedItem(item)
        setIsDeleteDialogOpen(true)
    }

    const currentData = activeTab === "categories" ? categories : units
    const filteredData = currentData.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const getStatusBadge = (status: string) => {
        return status === "Active" ? (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
            </Badge>
        ) : (
            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                Inactive
            </Badge>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Category & Unit Management</h1>
                    <p className="text-muted-foreground">
                        Manage product categories and units of measurement.
                    </p>
                </div>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{successMessage}</AlertDescription>
                </Alert>
            )}

            {/* Tabs */}
            <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
                <Button
                    variant={activeTab === "categories" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTab("categories")}
                    className="flex items-center gap-2"
                >
                    <Folder className="h-4 w-4" />
                    Categories
                </Button>
                <Button
                    variant={activeTab === "units" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTab("units")}
                    className="flex items-center gap-2"
                >
                    <Tag className="h-4 w-4" />
                    Units
                </Button>
            </div>

            {/* Search and Add */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder={`Search ${activeTab}...`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={resetForm}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add {activeTab === "categories" ? "Category" : "Unit"}
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add New {activeTab === "categories" ? "Category" : "Unit"}</DialogTitle>
                            <DialogDescription>
                                Create a new {activeTab === "categories" ? "category" : "unit"} for your products.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Name *</Label>
                                <Input
                                    id="name"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    placeholder={`Enter ${activeTab === "categories" ? "category" : "unit"} name`}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                    placeholder="Enter description"
                                    rows={3}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleAdd} disabled={!formData.name.trim()}>
                                Add {activeTab === "categories" ? "Category" : "Unit"}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Data Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>{activeTab === "categories" ? "Categories" : "Units"} ({filteredData.length})</span>
                        <div className="flex gap-2">
                            <Badge variant="secondary">
                                Active: {filteredData.filter(item => item.status === "Active").length}
                            </Badge>
                            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                                Inactive: {filteredData.filter(item => item.status === "Inactive").length}
                            </Badge>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredData.length === 0 ? (
                        <div className="text-center py-8">
                            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">
                                No {activeTab} found
                            </h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm
                                    ? "Try adjusting your search criteria."
                                    : `Start by adding your first ${activeTab === "categories" ? "category" : "unit"}.`
                                }
                            </p>
                            <Button onClick={() => setIsAddDialogOpen(true)}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add {activeTab === "categories" ? "Category" : "Unit"}
                            </Button>
                        </div>
                    ) : (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Products</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredData.map((item) => (
                                    <TableRow key={item.id}>
                                        <TableCell>
                                            <div className="font-medium">{item.name}</div>
                                        </TableCell>
                                        <TableCell className="text-muted-foreground">
                                            {item.description || "No description"}
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">
                                                {item.productCount} products
                                            </Badge>
                                        </TableCell>
                                        <TableCell>{getStatusBadge(item.status)}</TableCell>
                                        <TableCell className="text-muted-foreground text-sm">
                                            {new Date(item.createdAt).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="sm">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={() => openEditDialog(item)}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        onClick={() => openDeleteDialog(item)}
                                                        className="text-red-600 focus:text-red-600"
                                                        disabled={item.productCount > 0}
                                                    >
                                                        <Trash2 className="h-4 w-4 mr-2" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </CardContent>
            </Card>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit {activeTab === "categories" ? "Category" : "Unit"}</DialogTitle>
                        <DialogDescription>
                            Update the {activeTab === "categories" ? "category" : "unit"} information.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="edit-name">Name *</Label>
                            <Input
                                id="edit-name"
                                value={formData.name}
                                onChange={(e) => setFormData({...formData, name: e.target.value})}
                                placeholder={`Enter ${activeTab === "categories" ? "category" : "unit"} name`}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-description">Description</Label>
                            <Textarea
                                id="edit-description"
                                value={formData.description}
                                onChange={(e) => setFormData({...formData, description: e.target.value})}
                                placeholder="Enter description"
                                rows={3}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleEdit} disabled={!formData.name.trim()}>
                            Update {activeTab === "categories" ? "Category" : "Unit"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete {activeTab === "categories" ? "Category" : "Unit"}</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete "{selectedItem?.name}"? This action cannot be undone.
                            {selectedItem?.productCount > 0 && (
                                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded">
                                    <div className="flex items-center gap-2 text-orange-800">
                                        <AlertCircle className="h-4 w-4" />
                                        <span className="text-sm">
                                            This {activeTab === "categories" ? "category" : "unit"} is used by {selectedItem.productCount} products and cannot be deleted.
                                        </span>
                                    </div>
                                </div>
                            )}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button 
                            variant="destructive" 
                            onClick={handleDelete}
                            disabled={selectedItem?.productCount > 0}
                        >
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
