import { Request, Response } from "express";

export const getAllCategories = async (req: Request, res: Response) => {
    res.json("List of all the category");
}

export const getCategory = async (req: Request, res: Response) => {
    res.json("Single category Data");
}

export const addCategory = async (req: Request, res: Response) => {
    res.json("category Added");
}

export const editCategory = async (req: Request, res: Response) => {
    res.json("category Edited");
}

export const deleteCategory = async (req: Request, res: Response) => {
    res.json("Category Deleted");
}
