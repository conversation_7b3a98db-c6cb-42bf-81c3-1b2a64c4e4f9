import { Entity, Column, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class Product {
    @PrimaryGeneratedColumn()
    id?: number;

    @Column()
    name?: string;

    @Column()
    category_id?: number;

    @Column()
    brand?: string;

    @Column()
    imageUrl?: string;

    @Column()
    desc?: string;

    @Column()
    purchasePrice?: number;

    @Column()
    sellingPrice?: number;

    @Column()
    stock?: number;

    @Column()
    minStock?: number;

    @Column()
    unit_id?: string;

    @Column()
    barcode?: string;

    @Column()
    supplier?: string;
}