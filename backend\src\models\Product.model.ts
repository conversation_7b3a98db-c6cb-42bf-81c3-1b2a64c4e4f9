import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Category } from "./Category.model";
import { Units } from "./Units.model";
import { Suppliers } from "./Suppliers.model";

@Entity("products")
export class Product {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @ManyToOne(() => Category)
  @JoinColumn({ name: "category_id" })
  category!: Category;

  @Column({ nullable: true })
  brand!: string;

  @Column({ nullable: true })
  imageUrl!: string;

  @Column({ nullable: true })
  desc!: string;

  @Column("decimal", { precision: 10, scale: 2, nullable: true })
  purchasePrice!: number;

  @Column("decimal", { precision: 10, scale: 2, nullable: true })
  sellingPrice!: number;

  @Column({ nullable: true })
  stock!: number;

  @Column({ nullable: true })
  minStock!: number;

  @ManyToOne(() => Units)
  @JoinColumn({ name: "unit_id" })
  unit!: Units;

  @Column({ nullable: true })
  barcode!: string;

  @ManyToOne(() => Suppliers)
  @JoinColumn({ name: "supplier_id" })
  supplier!: Suppliers;

  @CreateDateColumn({ name: "created_at", type: "datetime", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @UpdateDateColumn({ name: "updated_at", type: "datetime", default: () => "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
