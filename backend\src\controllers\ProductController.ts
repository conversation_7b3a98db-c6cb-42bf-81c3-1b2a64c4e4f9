import { Request, Response } from "express";

export const getAllProducts = async (req: Request, res: Response) => {
    res.json("List of all the products");
}

export const getSingleProduct = async (req: Request, res: Response) => {
    res.json("Single product Data");
}

export const addProduct = async (req: Request, res: Response) => {
    res.json("Product Added");
}

export const editProduct = async (req: Request, res: Response) => {
    res.json("Product Edited");
}

export const deleteProduct = async (req: Request, res: Response) => {
    res.json("Product Deleted");
}
