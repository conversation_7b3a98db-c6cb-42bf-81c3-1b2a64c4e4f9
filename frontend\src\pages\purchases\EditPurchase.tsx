import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    ArrowLeft,
    Plus,
    Trash2,
    Save,
    Calculator,
    Package,
    AlertTriangle,
    Edit
} from "lucide-react"

interface PurchaseItem {
    id: string
    productId: string
    productName: string
    unit: string
    quantity: number
    purchasePrice: number
    total: number
    currentStock: number
}

interface Supplier {
    id: string
    name: string
    phone: string
    gst?: string
}

export default function EditPurchase() {
    const navigate = useNavigate()
    const { id } = useParams()
    const [isLoading, setIsLoading] = useState(false)
    const [showAddItemDialog, setShowAddItemDialog] = useState(false)
    const [isLocked, setIsLocked] = useState(false)

    // Form state
    const [formData, setFormData] = useState({
        supplierId: "1",
        invoiceNumber: "INV-2024-001",
        purchaseDate: "2024-01-15",
        paymentType: "cash",
        notes: "Regular monthly stock purchase"
    })

    const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([
        {
            id: "1",
            productId: "1",
            productName: "Basmati Rice 1kg",
            unit: "kg",
            quantity: 50,
            purchasePrice: 120,
            total: 6000,
            currentStock: 75
        },
        {
            id: "2",
            productId: "2",
            productName: "Tata Salt 1kg",
            unit: "kg",
            quantity: 100,
            purchasePrice: 25,
            total: 2500,
            currentStock: 115
        }
    ])

    const [newItem, setNewItem] = useState({
        productId: "",
        quantity: 1,
        purchasePrice: 0
    })

    // Mock data
    const suppliers: Supplier[] = [
        { id: "1", name: "ABC Distributors", phone: "+91 98765 43210", gst: "27ABCDE1234F1Z5" },
        { id: "2", name: "XYZ Suppliers", phone: "+91 87654 32109", gst: "27XYZAB5678G2Y4" },
        { id: "3", name: "Fresh Foods Ltd", phone: "+91 76543 21098" }
    ]

    const products = [
        { id: "1", name: "Basmati Rice 1kg", unit: "kg", currentStock: 75, lastPrice: 120 },
        { id: "2", name: "Tata Salt 1kg", unit: "kg", currentStock: 115, lastPrice: 25 },
        { id: "3", name: "Amul Milk 500ml", unit: "piece", currentStock: 95, lastPrice: 28 },
        { id: "4", name: "Maggi Noodles", unit: "piece", currentStock: 40, lastPrice: 15 }
    ]

    const paymentTypes = [
        { value: "cash", label: "Cash" },
        { value: "credit", label: "Credit" },
        { value: "bank_transfer", label: "Bank Transfer" },
        { value: "cheque", label: "Cheque" }
    ]

    // Check if purchase is locked (e.g., already synced or accounted)
    useEffect(() => {
        // In real app, check if purchase can be edited
        setIsLocked(false) // For demo, allow editing
    }, [id])

    // Calculate totals
    const subtotal = purchaseItems.reduce((sum, item) => sum + item.total, 0)
    const tax = subtotal * 0.18 // 18% GST
    const grandTotal = subtotal + tax

    const handleAddItem = () => {
        if (!newItem.productId || newItem.quantity <= 0 || newItem.purchasePrice <= 0) {
            return
        }

        const product = products.find(p => p.id === newItem.productId)
        if (!product) return

        const item: PurchaseItem = {
            id: Date.now().toString(),
            productId: newItem.productId,
            productName: product.name,
            unit: product.unit,
            quantity: newItem.quantity,
            purchasePrice: newItem.purchasePrice,
            total: newItem.quantity * newItem.purchasePrice,
            currentStock: product.currentStock
        }

        setPurchaseItems([...purchaseItems, item])
        setNewItem({ productId: "", quantity: 1, purchasePrice: 0 })
        setShowAddItemDialog(false)
    }

    const handleRemoveItem = (itemId: string) => {
        setPurchaseItems(purchaseItems.filter(item => item.id !== itemId))
    }

    const handleUpdateItem = (itemId: string, field: string, value: number) => {
        setPurchaseItems(items => items.map(item => {
            if (item.id === itemId) {
                const updatedItem = { ...item, [field]: value }
                if (field === 'quantity' || field === 'purchasePrice') {
                    updatedItem.total = updatedItem.quantity * updatedItem.purchasePrice
                }
                return updatedItem
            }
            return item
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!formData.supplierId || !formData.invoiceNumber || purchaseItems.length === 0) {
            return
        }

        setIsLoading(true)
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        setIsLoading(false)
        navigate(`/purchases/details/${id}`)
    }

    const selectedSupplier = suppliers.find(s => s.id === formData.supplierId)

    if (isLocked) {
        return (
            <div className="flex flex-1 flex-col gap-6">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => navigate(`/purchases/details/${id}`)}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Edit Purchase</h1>
                        <p className="text-muted-foreground">Purchase ID: {id}</p>
                    </div>
                </div>

                <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                        This purchase cannot be edited as it has been locked (already synced or accounted).
                        Contact your administrator if you need to make changes.
                    </AlertDescription>
                </Alert>
            </div>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate(`/purchases/details/${id}`)}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Edit Purchase</h1>
                    <p className="text-muted-foreground">
                        Modify purchase details and update inventory accordingly.
                    </p>
                </div>
            </div>

            <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                    Editing this purchase will automatically adjust stock levels. 
                    Please ensure all changes are accurate before saving.
                </AlertDescription>
            </Alert>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Purchase Details */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Package className="h-5 w-5" />
                            Purchase Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="supplier">Supplier *</Label>
                                <Select
                                    value={formData.supplierId}
                                    onValueChange={(value) => setFormData({...formData, supplierId: value})}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select supplier" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {suppliers.map((supplier) => (
                                            <SelectItem key={supplier.id} value={supplier.id}>
                                                <div className="flex flex-col">
                                                    <span>{supplier.name}</span>
                                                    <span className="text-xs text-muted-foreground">{supplier.phone}</span>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {selectedSupplier?.gst && (
                                    <p className="text-xs text-muted-foreground">GST: {selectedSupplier.gst}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="invoiceNumber">Invoice Number *</Label>
                                <Input
                                    id="invoiceNumber"
                                    value={formData.invoiceNumber}
                                    onChange={(e) => setFormData({...formData, invoiceNumber: e.target.value})}
                                    placeholder="Enter invoice number"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="purchaseDate">Purchase Date *</Label>
                                <Input
                                    id="purchaseDate"
                                    type="date"
                                    value={formData.purchaseDate}
                                    onChange={(e) => setFormData({...formData, purchaseDate: e.target.value})}
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="paymentType">Payment Type *</Label>
                                <Select
                                    value={formData.paymentType}
                                    onValueChange={(value) => setFormData({...formData, paymentType: value})}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select payment type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {paymentTypes.map((type) => (
                                            <SelectItem key={type.value} value={type.value}>
                                                {type.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Purchase Items */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Purchase Items</CardTitle>
                        <Dialog open={showAddItemDialog} onOpenChange={setShowAddItemDialog}>
                            <DialogTrigger asChild>
                                <Button size="sm">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Item
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Add Purchase Item</DialogTitle>
                                    <DialogDescription>
                                        Select a product and enter purchase details.
                                    </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>Product *</Label>
                                        <Select
                                            value={newItem.productId}
                                            onValueChange={(value) => {
                                                const product = products.find(p => p.id === value)
                                                setNewItem({
                                                    ...newItem,
                                                    productId: value,
                                                    purchasePrice: product?.lastPrice || 0
                                                })
                                            }}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select product" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {products.map((product) => (
                                                    <SelectItem key={product.id} value={product.id}>
                                                        <div className="flex flex-col">
                                                            <span>{product.name}</span>
                                                            <span className="text-xs text-muted-foreground">
                                                                Stock: {product.currentStock} {product.unit} | Last: ₹{product.lastPrice}
                                                            </span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="grid gap-4 grid-cols-2">
                                        <div className="space-y-2">
                                            <Label>Quantity *</Label>
                                            <Input
                                                type="number"
                                                min="1"
                                                value={newItem.quantity}
                                                onChange={(e) => setNewItem({...newItem, quantity: parseInt(e.target.value) || 1})}
                                            />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Purchase Price *</Label>
                                            <Input
                                                type="number"
                                                min="0"
                                                step="0.01"
                                                value={newItem.purchasePrice}
                                                onChange={(e) => setNewItem({...newItem, purchasePrice: parseFloat(e.target.value) || 0})}
                                            />
                                        </div>
                                    </div>
                                    {newItem.quantity > 0 && newItem.purchasePrice > 0 && (
                                        <div className="p-3 bg-muted rounded-lg">
                                            <p className="text-sm font-medium">
                                                Total: ₹{(newItem.quantity * newItem.purchasePrice).toFixed(2)}
                                            </p>
                                        </div>
                                    )}
                                </div>
                                <DialogFooter>
                                    <Button type="button" variant="outline" onClick={() => setShowAddItemDialog(false)}>
                                        Cancel
                                    </Button>
                                    <Button type="button" onClick={handleAddItem}>
                                        Add Item
                                    </Button>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                    </CardHeader>
                    <CardContent>
                        {purchaseItems.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No items added yet. Click "Add Item" to start.</p>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Product</TableHead>
                                        <TableHead>Unit</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Price</TableHead>
                                        <TableHead>Total</TableHead>
                                        <TableHead>Stock</TableHead>
                                        <TableHead></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {purchaseItems.map((item) => (
                                        <TableRow key={item.id}>
                                            <TableCell className="font-medium">{item.productName}</TableCell>
                                            <TableCell>{item.unit}</TableCell>
                                            <TableCell>
                                                <Input
                                                    type="number"
                                                    min="1"
                                                    value={item.quantity}
                                                    onChange={(e) => handleUpdateItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                                                    className="w-20"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    step="0.01"
                                                    value={item.purchasePrice}
                                                    onChange={(e) => handleUpdateItem(item.id, 'purchasePrice', parseFloat(e.target.value) || 0)}
                                                    className="w-24"
                                                />
                                            </TableCell>
                                            <TableCell>₹{item.total.toFixed(2)}</TableCell>
                                            <TableCell>
                                                <Badge variant="outline">{item.currentStock}</Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleRemoveItem(item.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        )}
                    </CardContent>
                </Card>

                {/* Purchase Summary */}
                {purchaseItems.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calculator className="h-5 w-5" />
                                Purchase Summary
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>₹{subtotal.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>GST (18%):</span>
                                    <span>₹{tax.toFixed(2)}</span>
                                </div>
                                <div className="border-t pt-2">
                                    <div className="flex justify-between font-bold text-lg">
                                        <span>Grand Total:</span>
                                        <span>₹{grandTotal.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Submit Button */}
                <div className="flex justify-end gap-4">
                    <Button type="button" variant="outline" onClick={() => navigate(`/purchases/details/${id}`)}>
                        Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading || purchaseItems.length === 0}>
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Updating...
                            </>
                        ) : (
                            <>
                                <Save className="h-4 w-4 mr-2" />
                                Update Purchase
                            </>
                        )}
                    </Button>
                </div>
            </form>
        </div>
    )
}
