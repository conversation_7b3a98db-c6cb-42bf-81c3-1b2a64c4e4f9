import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
    ArrowLeft,
    Plus,
    Search,
    Filter,
    Download,
    MoreHorizontal,
    Eye,
    Edit,
    Trash2,
    Calendar,
    User,
    Package,
    RefreshCw
} from "lucide-react"

interface Purchase {
    id: string
    invoiceNumber: string
    supplier: string
    date: string
    amount: number
    status: "paid" | "pending" | "partially_paid"
    items: number
    paymentType: string
}

export default function PurchaseList() {
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)
    const [searchTerm, setSearchTerm] = useState("")
    const [statusFilter, setStatusFilter] = useState("all")
    const [supplierFilter, setSupplierFilter] = useState("all")
    const [dateRange, setDateRange] = useState("all")

    // Mock data
    const purchases: Purchase[] = [
        {
            id: "PUR-001",
            invoiceNumber: "INV-2024-001",
            supplier: "ABC Distributors",
            date: "2024-01-15",
            amount: 12500,
            status: "paid",
            items: 5,
            paymentType: "cash"
        },
        {
            id: "PUR-002",
            invoiceNumber: "INV-2024-002",
            supplier: "XYZ Suppliers",
            date: "2024-01-14",
            amount: 8750,
            status: "pending",
            items: 3,
            paymentType: "credit"
        },
        {
            id: "PUR-003",
            invoiceNumber: "INV-2024-003",
            supplier: "Fresh Foods Ltd",
            date: "2024-01-13",
            amount: 15200,
            status: "partially_paid",
            items: 8,
            paymentType: "bank_transfer"
        },
        {
            id: "PUR-004",
            invoiceNumber: "INV-2024-004",
            supplier: "ABC Distributors",
            date: "2024-01-12",
            amount: 9800,
            status: "paid",
            items: 4,
            paymentType: "cash"
        },
        {
            id: "PUR-005",
            invoiceNumber: "INV-2024-005",
            supplier: "Quality Goods Co",
            date: "2024-01-11",
            amount: 22000,
            status: "pending",
            items: 12,
            paymentType: "credit"
        }
    ]

    const suppliers = ["ABC Distributors", "XYZ Suppliers", "Fresh Foods Ltd", "Quality Goods Co"]

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "paid":
                return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Paid</Badge>
            case "pending":
                return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>
            case "partially_paid":
                return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Partial</Badge>
            default:
                return <Badge variant="secondary">{status}</Badge>
        }
    }

    const getPaymentTypeBadge = (type: string) => {
        const typeMap: { [key: string]: string } = {
            cash: "Cash",
            credit: "Credit",
            bank_transfer: "Bank Transfer",
            cheque: "Cheque"
        }
        return <Badge variant="outline">{typeMap[type] || type}</Badge>
    }

    // Filter purchases based on search and filters
    const filteredPurchases = purchases.filter(purchase => {
        const matchesSearch = 
            purchase.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
            purchase.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
            purchase.id.toLowerCase().includes(searchTerm.toLowerCase())
        
        const matchesStatus = statusFilter === "all" || purchase.status === statusFilter
        const matchesSupplier = supplierFilter === "all" || purchase.supplier === supplierFilter
        
        return matchesSearch && matchesStatus && matchesSupplier
    })

    const handleRefresh = async () => {
        setIsLoading(true)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setIsLoading(false)
    }

    const handleExport = () => {
        // Export functionality would be implemented here
        console.log("Exporting purchases...")
    }

    const handleDelete = (purchaseId: string) => {
        // Delete functionality would be implemented here
        console.log("Deleting purchase:", purchaseId)
    }

    // Calculate summary stats
    const totalAmount = filteredPurchases.reduce((sum, p) => sum + p.amount, 0)
    const paidAmount = filteredPurchases.filter(p => p.status === "paid").reduce((sum, p) => sum + p.amount, 0)
    const pendingAmount = filteredPurchases.filter(p => p.status === "pending").reduce((sum, p) => sum + p.amount, 0)

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate("/purchases")}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                </Button>
                <div className="flex-1">
                    <h1 className="text-2xl font-bold">Purchase List</h1>
                    <p className="text-muted-foreground">
                        View and manage all purchase records.
                    </p>
                </div>
                <Button onClick={() => navigate("/purchases/add")}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Purchase
                </Button>
            </div>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                                <p className="text-2xl font-bold">₹{totalAmount.toLocaleString()}</p>
                            </div>
                            <Package className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Paid Amount</p>
                                <p className="text-2xl font-bold text-green-600">₹{paidAmount.toLocaleString()}</p>
                            </div>
                            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                                <div className="h-4 w-4 rounded-full bg-green-600"></div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Pending Amount</p>
                                <p className="text-2xl font-bold text-yellow-600">₹{pendingAmount.toLocaleString()}</p>
                            </div>
                            <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                                <div className="h-4 w-4 rounded-full bg-yellow-600"></div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters and Search */}
            <Card>
                <CardHeader>
                    <CardTitle>Filters</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by invoice, supplier, or ID..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                <SelectItem value="paid">Paid</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="partially_paid">Partially Paid</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={supplierFilter} onValueChange={setSupplierFilter}>
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Supplier" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Suppliers</SelectItem>
                                {suppliers.map((supplier) => (
                                    <SelectItem key={supplier} value={supplier}>
                                        {supplier}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
                                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                                Refresh
                            </Button>
                            <Button variant="outline" size="sm" onClick={handleExport}>
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Purchase Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Purchases ({filteredPurchases.length})</CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Purchase ID</TableHead>
                                <TableHead>Invoice</TableHead>
                                <TableHead>Supplier</TableHead>
                                <TableHead>Date</TableHead>
                                <TableHead>Items</TableHead>
                                <TableHead>Amount</TableHead>
                                <TableHead>Payment</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredPurchases.map((purchase) => (
                                <TableRow key={purchase.id} className="cursor-pointer hover:bg-muted/50">
                                    <TableCell className="font-medium">{purchase.id}</TableCell>
                                    <TableCell>{purchase.invoiceNumber}</TableCell>
                                    <TableCell>{purchase.supplier}</TableCell>
                                    <TableCell>{new Date(purchase.date).toLocaleDateString()}</TableCell>
                                    <TableCell>{purchase.items} items</TableCell>
                                    <TableCell className="font-medium">₹{purchase.amount.toLocaleString()}</TableCell>
                                    <TableCell>{getPaymentTypeBadge(purchase.paymentType)}</TableCell>
                                    <TableCell>{getStatusBadge(purchase.status)}</TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => navigate(`/purchases/details/${purchase.id}`)}>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View Details
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => navigate(`/purchases/edit/${purchase.id}`)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem 
                                                    onClick={() => handleDelete(purchase.id)}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    
                    {filteredPurchases.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No purchases found matching your criteria.</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
