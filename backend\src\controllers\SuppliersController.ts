import { Request, Response } from "express";

export const getAllSuppliers = async (req: Request, res: Response) => {
    res.json("List of all the suppliers");
}

export const getSupplier = async (req: Request, res: Response) => {
    res.json("Single suppiers Data");
}

export const addSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Added");
}

export const editSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Edited");
}

export const deleteSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Deleted");
}
