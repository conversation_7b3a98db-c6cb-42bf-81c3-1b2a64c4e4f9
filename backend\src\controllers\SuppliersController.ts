import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { Suppliers } from "../models/Suppliers.model";

export const getAllSuppliers = async (req: Request, res: Response) => {
    const suppliers = await AppDataSource.getRepository(Suppliers).find();
    res.json(suppliers);
}

export const getSupplier = async (req: Request, res: Response) => {
    res.json("Single suppiers Data");
}

export const addSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Added");
}

export const editSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Edited");
}

export const deleteSupplier = async (req: Request, res: Response) => {
    res.json("Supplier Deleted");
}
