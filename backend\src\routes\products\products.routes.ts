import { Router } from "express";
import {
    addProduct, editProduct, getAllProducts, getSingleProduct, deleteProduct
} from '../../controllers/ProductController'

const router = Router();

// Get all products or single product if ID provided
router.get("/", getAllProducts);
router.get("/:id", getSingleProduct);
router.post("/", addProduct);   
router.put("/:id", editProduct);   
router.delete("/:id", deleteProduct);   

export default router;