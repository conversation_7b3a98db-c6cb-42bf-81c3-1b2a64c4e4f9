import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
    Truck,
    Plus,
    FileText,
    Users,
    TrendingUp,
    Package,
    AlertTriangle,
    Calendar,
    DollarSign,
    BarChart3
} from "lucide-react"

export default function Purchases() {
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)

    // Mock data for dashboard stats
    const stats = {
        totalPurchases: 156,
        monthlyPurchases: 23,
        totalAmount: 245000,
        monthlyAmount: 45000,
        pendingPayments: 15000,
        suppliers: 12,
        lowStockAlerts: 8
    }

    const recentPurchases = [
        {
            id: "PUR-001",
            supplier: "ABC Distributors",
            date: "2024-01-15",
            amount: 12500,
            status: "paid",
            items: 5
        },
        {
            id: "PUR-002",
            supplier: "XYZ Suppliers",
            date: "2024-01-14",
            amount: 8750,
            status: "pending",
            items: 3
        },
        {
            id: "PUR-003",
            supplier: "Fresh Foods Ltd",
            date: "2024-01-13",
            amount: 15200,
            status: "partially_paid",
            items: 8
        }
    ]

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "paid":
                return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Paid</Badge>
            case "pending":
                return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>
            case "partially_paid":
                return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Partial</Badge>
            default:
                return <Badge variant="secondary">{status}</Badge>
        }
    }

    const quickActions = [
        {
            title: "Add Purchase",
            description: "Record a new purchase from supplier",
            icon: Plus,
            action: () => navigate("/purchases/add"),
            color: "bg-blue-500"
        },
        {
            title: "View All Purchases",
            description: "Browse and manage all purchases",
            icon: FileText,
            action: () => navigate("/purchases/list"),
            color: "bg-green-500"
        },
        {
            title: "Manage Suppliers",
            description: "Add and manage supplier information",
            icon: Users,
            action: () => navigate("/purchases/suppliers"),
            color: "bg-purple-500"
        },
        {
            title: "Purchase Reports",
            description: "Generate reports and analytics",
            icon: BarChart3,
            action: () => navigate("/purchases/reports"),
            color: "bg-orange-500"
        }
    ]

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Purchase Management</h1>
                    <p className="text-muted-foreground">
                        Manage purchases, suppliers, and track inventory inflow.
                    </p>
                </div>
                <Button onClick={() => navigate("/purchases/add")}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Purchase
                </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
                        <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.totalPurchases}</div>
                        <p className="text-xs text-muted-foreground">
                            +{stats.monthlyPurchases} this month
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">₹{stats.totalAmount.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            +₹{stats.monthlyAmount.toLocaleString()} this month
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">₹{stats.pendingPayments.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            Requires attention
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.suppliers}</div>
                        <p className="text-xs text-muted-foreground">
                            Registered suppliers
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        {quickActions.map((action, index) => (
                            <div
                                key={index}
                                className="flex flex-col items-center p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                onClick={action.action}
                            >
                                <div className={`p-3 rounded-full ${action.color} text-white mb-3`}>
                                    <action.icon className="h-6 w-6" />
                                </div>
                                <h3 className="font-semibold text-center mb-1">{action.title}</h3>
                                <p className="text-sm text-muted-foreground text-center">{action.description}</p>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Recent Purchases */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Recent Purchases</CardTitle>
                    <Button variant="outline" size="sm" onClick={() => navigate("/purchases/list")}>
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {recentPurchases.map((purchase) => (
                            <div
                                key={purchase.id}
                                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer"
                                onClick={() => navigate(`/purchases/details/${purchase.id}`)}
                            >
                                <div className="flex items-center gap-4">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <Truck className="h-4 w-4 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="font-medium">{purchase.id}</p>
                                        <p className="text-sm text-muted-foreground">{purchase.supplier}</p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-4">
                                    <div className="text-right">
                                        <p className="font-medium">₹{purchase.amount.toLocaleString()}</p>
                                        <p className="text-sm text-muted-foreground">{purchase.items} items</p>
                                    </div>
                                    {getStatusBadge(purchase.status)}
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
