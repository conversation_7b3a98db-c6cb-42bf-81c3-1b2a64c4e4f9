import { createBrowserRouter } from "react-router-dom";
import { lazy } from "react";
const AppLayout = lazy(() => import("@/layout/AppLayout"));
const Dashboard = lazy(() => import("@/pages/Dashboard"));
const Inventory = lazy(() => import("@/pages/inventory/Inventory"));
const AddProduct = lazy(() => import("@/pages/inventory/products/AddProduct"));
const EditProduct = lazy(() => import("@/pages/inventory/products/EditProduct"));
const ProductDetails = lazy(() => import("@/pages/inventory/products/ProductDetails"));
const ProductList = lazy(() => import("@/pages/inventory/products/ProductList"));
const CategoryManagement = lazy(() => import("@/pages/inventory/categories/CategoryManagement"));
const UnitManagement = lazy(() => import("@/pages/inventory/units/UnitManagement"));
const StockManagement = lazy(() => import("@/pages/inventory/stock/StockManagement"));
const StockAdjustment = lazy(() => import("@/pages/inventory/stock/StockAdjustment"));
const StockLogs = lazy(() => import("@/pages/inventory/stock/StockLogs"));
const ImportExport = lazy(() => import("@/pages/inventory/import-export/ImportExport"));
const AlertDemo = lazy(() => import("@/components/common/AlertDemo"));
const Sales = lazy(() => import("@/pages/Sales"));
const Settings = lazy(() => import("@/pages/Settings"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const ErrorBoundary = lazy(() => import("@/components/ErrorBoundary"));

export const router = createBrowserRouter([
    {
        path: "/",
        element: <AppLayout />,
        errorElement: <ErrorBoundary />,
        children: [
            {
                index: true,
                element: <Dashboard />,
            },
            {
                path: "inventory",
                element: <Inventory />,
            },
            // Product Management Routes
            {
                path: "inventory/products",
                element: <ProductList />,
            },
            {
                path: "inventory/products/add",
                element: <AddProduct />,
            },
            {
                path: "inventory/products/edit/:id",
                element: <EditProduct />,
            },
            {
                path: "inventory/products/details/:id",
                element: <ProductDetails />,
            },
            // Category & Unit Management Routes
            {
                path: "inventory/categories",
                element: <CategoryManagement />,
            },
            {
                path: "inventory/units",
                element: <UnitManagement />,
            },
            // Stock Management Routes
            {
                path: "inventory/stock",
                element: <StockManagement />,
            },
            {
                path: "inventory/stock/adjust",
                element: <StockAdjustment />,
            },
            {
                path: "inventory/stock/logs",
                element: <StockLogs />,
            },
            // Import/Export Routes
            {
                path: "inventory/import-export",
                element: <ImportExport />,
            },
            {
                path: "sales",
                element: <Sales />,
            },
            {
                path: "customers",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Customers</h1><p>Customer management coming soon...</p></div>,
            },
            {
                path: "reports",
                element: <div className="p-6"><h1 className="text-2xl font-bold">Reports</h1><p>Reports and analytics coming soon...</p></div>,
            },
            {
                path: "settings",
                element: <Settings />,
            },
            {
                path: "alerts-demo",
                element: <AlertDemo />,
            },
            {
                path: "*",
                element: <NotFound />,
            }
        ]
    }
])