import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    ArrowLeft,
    Download,
    FileText,
    Calendar,
    TrendingUp,
    Package,
    Users,
    DollarSign,
    BarChart3,
    Filter,
    RefreshCw
} from "lucide-react"

interface ReportData {
    period: string
    totalPurchases: number
    totalAmount: number
    totalItems: number
    uniqueSuppliers: number
    avgPurchaseValue: number
    topSupplier: string
    topProduct: string
}

interface PurchaseSummary {
    supplier: string
    purchases: number
    amount: number
    lastPurchase: string
}

export default function PurchaseReports() {
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)
    const [reportType, setReportType] = useState("summary")
    const [dateRange, setDateRange] = useState("this_month")
    const [startDate, setStartDate] = useState("")
    const [endDate, setEndDate] = useState("")
    const [selectedSupplier, setSelectedSupplier] = useState("all")

    // Mock data
    const reportData: ReportData = {
        period: "January 2024",
        totalPurchases: 45,
        totalAmount: 285000,
        totalItems: 1250,
        uniqueSuppliers: 8,
        avgPurchaseValue: 6333,
        topSupplier: "ABC Distributors",
        topProduct: "Basmati Rice 1kg"
    }

    const supplierSummary: PurchaseSummary[] = [
        {
            supplier: "ABC Distributors",
            purchases: 12,
            amount: 85000,
            lastPurchase: "2024-01-15"
        },
        {
            supplier: "XYZ Suppliers",
            purchases: 8,
            amount: 65000,
            lastPurchase: "2024-01-14"
        },
        {
            supplier: "Fresh Foods Ltd",
            purchases: 10,
            amount: 55000,
            lastPurchase: "2024-01-13"
        },
        {
            supplier: "Quality Goods Co",
            purchases: 6,
            amount: 45000,
            lastPurchase: "2024-01-12"
        },
        {
            supplier: "Premium Supplies",
            purchases: 5,
            amount: 35000,
            lastPurchase: "2024-01-11"
        }
    ]

    const productSummary = [
        { product: "Basmati Rice 1kg", quantity: 250, amount: 30000, purchases: 8 },
        { product: "Tata Salt 1kg", quantity: 500, amount: 12500, purchases: 12 },
        { product: "Amul Milk 500ml", quantity: 300, amount: 8400, purchases: 6 },
        { product: "Maggi Noodles", quantity: 200, amount: 3000, purchases: 4 },
        { product: "Britannia Biscuits", quantity: 150, amount: 5250, purchases: 5 }
    ]

    const suppliers = ["ABC Distributors", "XYZ Suppliers", "Fresh Foods Ltd", "Quality Goods Co", "Premium Supplies"]

    const handleGenerateReport = async () => {
        setIsLoading(true)
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        setIsLoading(false)
    }

    const handleExportReport = (format: string) => {
        console.log(`Exporting report in ${format} format...`)
        // Export functionality would be implemented here
    }

    const getDateRangeLabel = () => {
        switch (dateRange) {
            case "today": return "Today"
            case "yesterday": return "Yesterday"
            case "this_week": return "This Week"
            case "last_week": return "Last Week"
            case "this_month": return "This Month"
            case "last_month": return "Last Month"
            case "this_year": return "This Year"
            case "custom": return "Custom Range"
            default: return "This Month"
        }
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate("/purchases")}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                </Button>
                <div className="flex-1">
                    <h1 className="text-2xl font-bold">Purchase Reports</h1>
                    <p className="text-muted-foreground">
                        Generate detailed reports and analytics for purchase data.
                    </p>
                </div>
            </div>

            {/* Report Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="h-5 w-5" />
                        Report Filters
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-2">
                            <Label>Report Type</Label>
                            <Select value={reportType} onValueChange={setReportType}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="summary">Purchase Summary</SelectItem>
                                    <SelectItem value="supplier">Supplier Analysis</SelectItem>
                                    <SelectItem value="product">Product Analysis</SelectItem>
                                    <SelectItem value="payment">Payment Analysis</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label>Date Range</Label>
                            <Select value={dateRange} onValueChange={setDateRange}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="today">Today</SelectItem>
                                    <SelectItem value="yesterday">Yesterday</SelectItem>
                                    <SelectItem value="this_week">This Week</SelectItem>
                                    <SelectItem value="last_week">Last Week</SelectItem>
                                    <SelectItem value="this_month">This Month</SelectItem>
                                    <SelectItem value="last_month">Last Month</SelectItem>
                                    <SelectItem value="this_year">This Year</SelectItem>
                                    <SelectItem value="custom">Custom Range</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {dateRange === "custom" && (
                            <>
                                <div className="space-y-2">
                                    <Label>Start Date</Label>
                                    <Input
                                        type="date"
                                        value={startDate}
                                        onChange={(e) => setStartDate(e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label>End Date</Label>
                                    <Input
                                        type="date"
                                        value={endDate}
                                        onChange={(e) => setEndDate(e.target.value)}
                                    />
                                </div>
                            </>
                        )}

                        <div className="space-y-2">
                            <Label>Supplier</Label>
                            <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Suppliers</SelectItem>
                                    {suppliers.map((supplier) => (
                                        <SelectItem key={supplier} value={supplier}>
                                            {supplier}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="flex gap-2 mt-4">
                        <Button onClick={handleGenerateReport} disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    Generating...
                                </>
                            ) : (
                                <>
                                    <BarChart3 className="h-4 w-4 mr-2" />
                                    Generate Report
                                </>
                            )}
                        </Button>
                        <Button variant="outline" onClick={() => handleExportReport("excel")}>
                            <Download className="h-4 w-4 mr-2" />
                            Export Excel
                        </Button>
                        <Button variant="outline" onClick={() => handleExportReport("pdf")}>
                            <FileText className="h-4 w-4 mr-2" />
                            Export PDF
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Report Summary */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        Report Summary - {getDateRangeLabel()}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                                    <p className="text-2xl font-bold">{reportData.totalPurchases}</p>
                                </div>
                                <Package className="h-8 w-8 text-blue-600" />
                            </div>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                                    <p className="text-2xl font-bold">₹{reportData.totalAmount.toLocaleString()}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-green-600" />
                            </div>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                                    <p className="text-2xl font-bold">{reportData.totalItems.toLocaleString()}</p>
                                </div>
                                <Package className="h-8 w-8 text-purple-600" />
                            </div>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Avg Purchase Value</p>
                                    <p className="text-2xl font-bold">₹{reportData.avgPurchaseValue.toLocaleString()}</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-orange-600" />
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <div className="grid gap-6 lg:grid-cols-2">
                {/* Supplier Performance */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Top Suppliers
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Supplier</TableHead>
                                    <TableHead>Purchases</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Last Purchase</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {supplierSummary.map((supplier, index) => (
                                    <TableRow key={supplier.supplier}>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                                                    {index + 1}
                                                </Badge>
                                                <span className="font-medium">{supplier.supplier}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>{supplier.purchases}</TableCell>
                                        <TableCell>₹{supplier.amount.toLocaleString()}</TableCell>
                                        <TableCell>{new Date(supplier.lastPurchase).toLocaleDateString()}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>

                {/* Product Performance */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Package className="h-5 w-5" />
                            Top Products
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Product</TableHead>
                                    <TableHead>Quantity</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Orders</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {productSummary.map((product, index) => (
                                    <TableRow key={product.product}>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                                                    {index + 1}
                                                </Badge>
                                                <span className="font-medium">{product.product}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>{product.quantity}</TableCell>
                                        <TableCell>₹{product.amount.toLocaleString()}</TableCell>
                                        <TableCell>{product.purchases}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>

            {/* Key Insights */}
            <Card>
                <CardHeader>
                    <CardTitle>Key Insights</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="p-4 bg-blue-50 rounded-lg">
                            <h4 className="font-semibold text-blue-900 mb-2">Top Performing Supplier</h4>
                            <p className="text-blue-800">
                                <strong>{reportData.topSupplier}</strong> leads with the highest purchase volume, 
                                contributing significantly to your inventory growth.
                            </p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-lg">
                            <h4 className="font-semibold text-green-900 mb-2">Most Purchased Product</h4>
                            <p className="text-green-800">
                                <strong>{reportData.topProduct}</strong> is your most frequently purchased item, 
                                indicating strong demand and good supplier relationship.
                            </p>
                        </div>
                        <div className="p-4 bg-purple-50 rounded-lg">
                            <h4 className="font-semibold text-purple-900 mb-2">Purchase Frequency</h4>
                            <p className="text-purple-800">
                                You're averaging <strong>{Math.round(reportData.totalPurchases / 30)} purchases per day</strong> this month, 
                                showing consistent inventory management.
                            </p>
                        </div>
                        <div className="p-4 bg-orange-50 rounded-lg">
                            <h4 className="font-semibold text-orange-900 mb-2">Supplier Diversity</h4>
                            <p className="text-orange-800">
                                Working with <strong>{reportData.uniqueSuppliers} different suppliers</strong> provides 
                                good risk distribution and competitive pricing opportunities.
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
