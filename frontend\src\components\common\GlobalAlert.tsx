import React from "react"
import {
    Check<PERSON>ir<PERSON>,
    <PERSON><PERSON><PERSON>riangle,
    <PERSON><PERSON><PERSON>cle,
    Info,
    X
} from "lucide-react"
import { cn } from "@/lib/utils"
import "./alert-animations.css"

export type AlertType = "success" | "warning" | "error" | "info"

interface GlobalAlertProps {
    message: string
    type: AlertType
    onClose?: () => void
    className?: string
    showCloseButton?: boolean
    showProgress?: boolean
    duration?: number
}

const alertConfig = {
    success: {
        icon: CheckCircle,
        className: "border-green-200 text-green-800 bg-gradient-to-r from-green-50 to-green-100 dark:border-green-800 dark:text-green-200 dark:from-green-950 dark:to-green-900 shadow-lg shadow-green-500/20",
        iconClassName: "text-green-600 dark:text-green-400",
        iconBgClassName: "bg-green-100 dark:bg-green-800/50"
    },
    warning: {
        icon: AlertTriangle,
        className: "border-orange-200 text-orange-800 bg-gradient-to-r from-orange-50 to-orange-100 dark:border-orange-800 dark:text-orange-200 dark:from-orange-950 dark:to-orange-900 shadow-lg shadow-orange-500/20",
        iconClassName: "text-orange-600 dark:text-orange-400",
        iconBgClassName: "bg-orange-100 dark:bg-orange-800/50"
    },
    error: {
        icon: XCircle,
        className: "border-red-200 text-red-800 bg-gradient-to-r from-red-50 to-red-100 dark:border-red-800 dark:text-red-200 dark:from-red-950 dark:to-red-900 shadow-lg shadow-red-500/20",
        iconClassName: "text-red-600 dark:text-red-400",
        iconBgClassName: "bg-red-100 dark:bg-red-800/50"
    },
    info: {
        icon: Info,
        className: "border-blue-200 text-blue-800 bg-gradient-to-r from-blue-50 to-blue-100 dark:border-blue-800 dark:text-blue-200 dark:from-blue-950 dark:to-blue-900 shadow-lg shadow-blue-500/20",
        iconClassName: "text-blue-600 dark:text-blue-400",
        iconBgClassName: "bg-blue-100 dark:bg-blue-800/50"
    }
}

function GlobalAlert({
    message,
    type,
    onClose,
    className,
    showCloseButton = true,
    showProgress = false,
    duration = 5000
}: GlobalAlertProps) {
    const config = alertConfig[type]
    const IconComponent = config.icon
    const [progress, setProgress] = React.useState(100)

    React.useEffect(() => {
        if (showProgress && duration > 0 && onClose) {
            const interval = setInterval(() => {
                setProgress((prev) => {
                    const newProgress = prev - (100 / (duration / 100))
                    if (newProgress <= 0) {
                        clearInterval(interval)
                        onClose()
                        return 0
                    }
                    return newProgress
                })
            }, 100)

            return () => clearInterval(interval)
        }
    }, [showProgress, duration, onClose])

    return (
        <div className={cn(
            "group relative overflow-hidden rounded-xl border backdrop-blur-sm transition-all duration-500 ease-out animate-in slide-in-from-top-4 fade-in-0 zoom-in-95",
            "hover:scale-[1.02] hover:shadow-xl",
            config.className,
            className
        )}>
            {/* Animated shimmer effect */}
            <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent -skew-x-12 animate-shimmer"
                    style={{
                        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                        animation: 'shimmer 3s infinite'
                    }} />
            </div>

            {/* Main content */}
            <div className="relative flex gap-4 p-4 items-center">
                {/* Icon with enhanced background */}
                <div className={cn(
                    "flex-shrink-0 rounded-full p-2.5 ring-2 ring-white/30 shadow-lg transition-transform duration-300 group-hover:scale-110",
                    config.iconBgClassName
                )}>
                    <IconComponent className={cn("h-5 w-5 drop-shadow-sm", config.iconClassName)} />
                </div>

                {/* Message content */}
                <div className="flex-1 min-w-0 ">
                    <p className="text-sm font-medium leading-relaxed tracking-wide">
                        {message}
                    </p>
                </div>

                {/* Close button */}
                {showCloseButton && onClose && (
                    <button
                        onClick={onClose}
                        className={cn(
                            "flex-shrink-0 rounded-lg p-2 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent",
                            "hover:bg-white/30 focus:bg-white/30 active:scale-95",
                            config.iconClassName,
                            "focus:ring-current shadow-sm"
                        )}
                        aria-label="Close alert"
                    >
                        <X className="h-4 w-4" />
                    </button>
                )}
            </div>

            {/* Progress bar */}
            {showProgress && duration > 0 && (
                <div className="absolute bottom-0 left-0 h-1 bg-black/10 w-full">
                    <div
                        className={cn(
                            "h-full transition-all duration-100 ease-linear bg-gradient-to-r",
                            type === "success" && "from-green-400 to-green-600",
                            type === "warning" && "from-orange-400 to-orange-600",
                            type === "error" && "from-red-400 to-red-600",
                            type === "info" && "from-blue-400 to-blue-600"
                        )}
                        style={{ width: `${progress}%` }}
                    />
                </div>
            )}

            {/* Bottom accent line (when no progress bar) */}
            {!showProgress && (
                <div className={cn(
                    "h-1 w-full bg-gradient-to-r opacity-70",
                    type === "success" && "from-green-400 to-green-600",
                    type === "warning" && "from-orange-400 to-orange-600",
                    type === "error" && "from-red-400 to-red-600",
                    type === "info" && "from-blue-400 to-blue-600"
                )} />
            )}
        </div>
    )
}



// Context for global alert management
interface AlertContextType {
    showAlert: (message: string, type: AlertType, duration?: number) => void
    showSuccess: (message: string, duration?: number) => void
    showWarning: (message: string, duration?: number) => void
    showError: (message: string, duration?: number) => void
    showInfo: (message: string, duration?: number) => void
}

const AlertContext = React.createContext<AlertContextType | undefined>(undefined)

export function AlertProvider({ children }: { children: React.ReactNode }) {
    const [alert, setAlert] = React.useState<{
        message: string
        type: AlertType
        id: string
    } | null>(null)

    const showAlert = React.useCallback((message: string, type: AlertType, duration: number = 5000) => {
        const id = Math.random().toString(36).substring(2, 11)
        setAlert({ message, type, id })

        if (duration > 0) {
            setTimeout(() => {
                setAlert(current => current?.id === id ? null : current)
            }, duration)
        }
    }, [])

    const hideAlert = React.useCallback(() => {
        setAlert(null)
    }, [])

    const showSuccess = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "success", duration)
    }, [showAlert])

    const showWarning = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "warning", duration)
    }, [showAlert])

    const showError = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "error", duration)
    }, [showAlert])

    const showInfo = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "info", duration)
    }, [showAlert])

    return (
        <AlertContext.Provider value={{ showAlert, showSuccess, showWarning, showError, showInfo }}>
            {children}
            {alert && (
                <div className="fixed top-6 right-6 z-50 w-full max-w-sm pointer-events-none">
                    <div className="pointer-events-auto">
                        <GlobalAlert
                            message={alert.message}
                            type={alert.type}
                            onClose={hideAlert}
                            showProgress={true}
                            duration={5000}
                        />
                    </div>
                </div>
            )}
        </AlertContext.Provider>
    )
}

export function useGlobalAlert() {
    const context = React.useContext(AlertContext)
    if (context === undefined) {
        throw new Error('useGlobalAlert must be used within an AlertProvider')
    }
    return context
}
