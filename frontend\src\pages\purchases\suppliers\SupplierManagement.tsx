import { useEffect, useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
    ArrowLeft,
    Plus,
    Search,
    MoreHorizontal,
    Edit,
    Trash2,
    Phone,
    MapPin,
    FileText,
    Users,
    TrendingUp,
    Package
} from "lucide-react"

interface Supplier {
    id: string
    name: string
    phone: string
    email?: string
    address: string
    gstNumber?: string
    totalPurchases: number
    totalAmount: number
    lastPurchase?: string
    status: 0 | 1
}

export default function SupplierManagement() {
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)
    const [searchTerm, setSearchTerm] = useState("")
    const [showAddDialog, setShowAddDialog] = useState(false)
    const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null)

    const [newSupplier, setNewSupplier] = useState({
        name: "",
        phone: "",
        email: "",
        address: "",
        gstNumber: ""
    })

    // Mock data
    const [suppliers, setSuppliers] = useState<Supplier[]>([
        {
            id: "1",
            name: "ABC Distributors",
            phone: "+91 98765 43210",
            email: "<EMAIL>",
            address: "123 Market Street, Mumbai, Maharashtra 400001",
            gstNumber: "27ABCDE1234F1Z5",
            totalPurchases: 25,
            totalAmount: 125000,
            lastPurchase: "2024-01-15",
            status: 1
        },
        {
            id: "2",
            name: "XYZ Suppliers",
            phone: "+91 87654 32109",
            email: "<EMAIL>",
            address: "456 Trade Center, Delhi, Delhi 110001",
            gstNumber: "27XYZAB5678G2Y4",
            totalPurchases: 18,
            totalAmount: 89000,
            lastPurchase: "2024-01-14",
            status: 1
        },
        {
            id: "3",
            name: "Fresh Foods Ltd",
            phone: "+91 76543 21098",
            address: "789 Food Complex, Bangalore, Karnataka 560001",
            totalPurchases: 12,
            totalAmount: 45000,
            lastPurchase: "2024-01-13",
            status: 1
        },
        {
            id: "4",
            name: "Quality Goods Co",
            phone: "+91 65432 10987",
            email: "<EMAIL>",
            address: "321 Industrial Area, Chennai, Tamil Nadu 600001",
            gstNumber: "33QGCDE9876H3W2",
            totalPurchases: 8,
            totalAmount: 32000,
            lastPurchase: "2024-01-10",
            status: 1
        }
    ]);

    useEffect(() => {
        const fetchSuppliers = async () => {
            try {
                setIsLoading(true);
                const response = await fetch('/api/suppliers');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log("Suppliers:", data);
                // console.log("oldSuppliers: ", suppliers);
                setSuppliers(data);
            } catch (error) {
                console.error("Failed to fetch suppliers:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchSuppliers();
    }, []);


    const filteredSuppliers = suppliers.filter(supplier =>
        supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.phone.includes(searchTerm) ||
        supplier.address.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const handleAddSupplier = () => {
        if (!newSupplier.name || !newSupplier.phone || !newSupplier.address) {
            return
        }

        const supplier: Supplier = {
            id: Date.now().toString(),
            ...newSupplier,
            totalPurchases: 0,
            totalAmount: 0,
            status: 1
        }

        setSuppliers([...suppliers, supplier])
        setNewSupplier({ name: "", phone: "", email: "", address: "", gstNumber: "" })
        setShowAddDialog(false)
    }

    const handleEditSupplier = (supplier: Supplier) => {
        setEditingSupplier(supplier)
        setNewSupplier({
            name: supplier.name,
            phone: supplier.phone,
            email: supplier.email || "",
            address: supplier.address,
            gstNumber: supplier.gstNumber || ""
        })
    }

    const handleUpdateSupplier = () => {
        if (!editingSupplier || !newSupplier.name || !newSupplier.phone || !newSupplier.address) {
            return
        }

        setSuppliers(suppliers.map(s =>
            s.id === editingSupplier.id
                ? { ...s, ...newSupplier }
                : s
        ))

        setEditingSupplier(null)
        setNewSupplier({ name: "", phone: "", email: "", address: "", gstNumber: "" })
    }

    const handleDeleteSupplier = (supplierId: string) => {
        setSuppliers(suppliers.filter(s => s.id !== supplierId))
    }

    const toggleSupplierStatus = (supplierId: string) => {
        setSuppliers(suppliers.map(s =>
            s.id === supplierId
                ? { ...s, status: s.status === 1 ? 0 : 1 }
                : s
        ))
    }

    const getStatusBadge = (status: string) => {
        return status === "active"
            ? <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
            : <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>
    }

    // Calculate summary stats
    const activeSuppliers = suppliers.filter(s => s.status === 1).length
    const totalPurchaseAmount = suppliers.reduce((sum, s) => sum + s.totalAmount, 0)
    const avgPurchaseAmount = suppliers.length > 0 ? totalPurchaseAmount / suppliers.length : 0

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate("/purchases")}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                </Button>
                <div className="flex-1">
                    <h1 className="text-2xl font-bold">Supplier Management</h1>
                    <p className="text-muted-foreground">
                        Manage supplier information and track purchase history.
                    </p>
                </div>
                <Dialog open={showAddDialog || !!editingSupplier} onOpenChange={(open) => {
                    if (!open) {
                        setShowAddDialog(false)
                        setEditingSupplier(null)
                        setNewSupplier({ name: "", phone: "", email: "", address: "", gstNumber: "" })
                    }
                }}>
                    <DialogTrigger asChild>
                        <Button onClick={() => setShowAddDialog(true)}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Supplier
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>
                                {editingSupplier ? "Edit Supplier" : "Add New Supplier"}
                            </DialogTitle>
                            <DialogDescription>
                                {editingSupplier
                                    ? "Update supplier information."
                                    : "Enter supplier details to add them to your database."
                                }
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Supplier Name *</Label>
                                <Input
                                    id="name"
                                    value={newSupplier.name}
                                    onChange={(e) => setNewSupplier({ ...newSupplier, name: e.target.value })}
                                    placeholder="Enter supplier name"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number *</Label>
                                <Input
                                    id="phone"
                                    value={newSupplier.phone}
                                    onChange={(e) => setNewSupplier({ ...newSupplier, phone: e.target.value })}
                                    placeholder="+91 XXXXX XXXXX"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={newSupplier.email}
                                    onChange={(e) => setNewSupplier({ ...newSupplier, email: e.target.value })}
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="address">Address *</Label>
                                <Textarea
                                    id="address"
                                    value={newSupplier.address}
                                    onChange={(e) => setNewSupplier({ ...newSupplier, address: e.target.value })}
                                    placeholder="Enter complete address"
                                    rows={3}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="gst">GST Number</Label>
                                <Input
                                    id="gst"
                                    value={newSupplier.gstNumber}
                                    onChange={(e) => setNewSupplier({ ...newSupplier, gstNumber: e.target.value })}
                                    placeholder="27ABCDE1234F1Z5"
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setShowAddDialog(false)
                                    setEditingSupplier(null)
                                    setNewSupplier({ name: "", phone: "", email: "", address: "", gstNumber: "" })
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="button"
                                onClick={editingSupplier ? handleUpdateSupplier : handleAddSupplier}
                            >
                                {editingSupplier ? "Update" : "Add"} Supplier
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Suppliers</p>
                                <p className="text-2xl font-bold">{suppliers.length}</p>
                            </div>
                            <Users className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Active Suppliers</p>
                                <p className="text-2xl font-bold text-green-600">{activeSuppliers}</p>
                            </div>
                            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                                <div className="h-4 w-4 rounded-full bg-green-600"></div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Avg Purchase Value</p>
                                <p className="text-2xl font-bold">₹{avgPurchaseAmount.toLocaleString()}</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search */}
            <Card>
                <CardContent className="p-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search suppliers by name, phone, or address..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Suppliers Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Suppliers ({filteredSuppliers.length})</CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Supplier</TableHead>
                                <TableHead>Contact</TableHead>
                                <TableHead>GST</TableHead>
                                <TableHead>Purchases</TableHead>
                                <TableHead>Total Amount</TableHead>
                                <TableHead>Last Purchase</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredSuppliers.map((supplier) => (
                                <TableRow key={supplier.id}>
                                    <TableCell>
                                        <div>
                                            <p className="font-medium">{supplier.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <MapPin className="h-3 w-3" />
                                                {supplier.address.split(',')[0]}
                                            </p>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div>
                                            <p className="flex items-center gap-1">
                                                <Phone className="h-3 w-3" />
                                                {supplier.phone}
                                            </p>
                                            {supplier.email && (
                                                <p className="text-sm text-muted-foreground">{supplier.email}</p>
                                            )}
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        {supplier.gstNumber ? (
                                            <Badge variant="outline" className="font-mono text-xs">
                                                {supplier.gstNumber}
                                            </Badge>
                                        ) : (
                                            <span className="text-muted-foreground">-</span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-1">
                                            <Package className="h-3 w-3" />
                                            {supplier.totalPurchases}
                                        </div>
                                    </TableCell>
                                    <TableCell className="font-medium">
                                        ₹{supplier.totalAmount.toLocaleString()}
                                    </TableCell>
                                    <TableCell>
                                        {supplier.lastPurchase ? (
                                            new Date(supplier.lastPurchase).toLocaleDateString()
                                        ) : (
                                            <span className="text-muted-foreground">Never</span>
                                        )}
                                    </TableCell>
                                    <TableCell>{getStatusBadge(supplier.status)}</TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => handleEditSupplier(supplier)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => toggleSupplierStatus(supplier.id)}>
                                                    <FileText className="mr-2 h-4 w-4" />
                                                    {supplier.status === 1 ? "Deactivate" : "Activate"}
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    onClick={() => handleDeleteSupplier(supplier.id)}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>

                    {filteredSuppliers.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No suppliers found matching your search.</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
