import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
    Package,
    Plus,
    Search,
    Filter,
    Eye,
    Edit,
    Trash2,
    MoreHorizontal,
    Download,
    Upload,
    ChevronDown,
    SortAsc,
    SortDesc,
    RefreshCw,
    AlertTriangle,
    CheckCircle,
    XCircle
} from "lucide-react"

// Mock data for products
const mockProducts = [
    {
        id: 1,
        name: "Basmati Rice 1kg",
        sku: "BR001",
        category: "Grains",
        unit: "kg",
        stock: 45,
        price: 120,
        status: "Active",
        lowStockThreshold: 10,
        lastUpdated: "2 hours ago"
    },
    {
        id: 2,
        name: "Tata Salt 1kg",
        sku: "TS001",
        category: "Spices",
        unit: "kg",
        stock: 8,
        price: 25,
        status: "Active",
        lowStockThreshold: 15,
        lastUpdated: "4 hours ago"
    },
    {
        id: 3,
        name: "Amul Milk 500ml",
        sku: "AM001",
        category: "Dairy",
        unit: "ml",
        stock: 0,
        price: 28,
        status: "Active",
        lowStockThreshold: 20,
        lastUpdated: "1 day ago"
    },
    {
        id: 4,
        name: "Maggi Noodles",
        sku: "MN001",
        category: "Instant Food",
        unit: "pack",
        stock: 67,
        price: 15,
        status: "Active",
        lowStockThreshold: 25,
        lastUpdated: "3 hours ago"
    },
    {
        id: 5,
        name: "Britannia Biscuits",
        sku: "BB001",
        category: "Snacks",
        unit: "pack",
        stock: 12,
        price: 35,
        status: "Active",
        lowStockThreshold: 15,
        lastUpdated: "5 hours ago"
    }
]

export default function ProductList() {
    const navigate = useNavigate()
    const [products, setProducts] = useState(mockProducts)
    const [searchTerm, setSearchTerm] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [sortBy, setSortBy] = useState<string>("name")
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
    const [filterStatus, setFilterStatus] = useState<string>("all")
    const [filterCategory, setFilterCategory] = useState<string>("all")
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [productToDelete, setProductToDelete] = useState<number | null>(null)

    // Get unique categories for filter
    const categories = Array.from(new Set(products.map(p => p.category)))

    // Filter and sort products
    const filteredProducts = products
        .filter(product => {
            const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.category.toLowerCase().includes(searchTerm.toLowerCase())
            const matchesStatus = filterStatus === "all" || 
                (filterStatus === "active" && product.status === "Active") ||
                (filterStatus === "low-stock" && product.stock <= product.lowStockThreshold && product.stock > 0) ||
                (filterStatus === "out-of-stock" && product.stock === 0)
            const matchesCategory = filterCategory === "all" || product.category === filterCategory
            return matchesSearch && matchesStatus && matchesCategory
        })
        .sort((a, b) => {
            let aValue = a[sortBy as keyof typeof a]
            let bValue = b[sortBy as keyof typeof b]

            if (typeof aValue === "string") aValue = aValue.toLowerCase()
            if (typeof bValue === "string") bValue = bValue.toLowerCase()

            if (sortOrder === "asc") {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
            }
        })

    const handleRefresh = async () => {
        setIsLoading(true)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setIsLoading(false)
    }

    const handleProductAction = (action: string, productId: number) => {
        switch (action) {
            case "view":
                navigate(`/inventory/products/details/${productId}`)
                break
            case "edit":
                navigate(`/inventory/products/edit/${productId}`)
                break
            case "delete":
                setProductToDelete(productId)
                setDeleteDialogOpen(true)
                break
            default:
                console.log(`${action} product with ID: ${productId}`)
        }
    }

    const handleDeleteProduct = () => {
        if (productToDelete) {
            setProducts(products.filter(p => p.id !== productToDelete))
            setDeleteDialogOpen(false)
            setProductToDelete(null)
        }
    }

    const getStockStatus = (product: any) => {
        if (product.stock === 0) {
            return { label: "Out of Stock", variant: "destructive" as const, icon: XCircle }
        } else if (product.stock <= product.lowStockThreshold) {
            return { label: "Low Stock", variant: "warning" as const, icon: AlertTriangle }
        } else {
            return { label: "In Stock", variant: "success" as const, icon: CheckCircle }
        }
    }

    const getStatusBadge = (product: any) => {
        const status = getStockStatus(product)
        const Icon = status.icon
        
        const variantClasses = {
            destructive: "bg-red-100 text-red-800 hover:bg-red-100",
            warning: "bg-orange-100 text-orange-800 hover:bg-orange-100",
            success: "bg-green-100 text-green-800 hover:bg-green-100"
        }

        return (
            <Badge className={variantClasses[status.variant]}>
                <Icon className="h-3 w-3 mr-1" />
                {status.label}
            </Badge>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Product Management</h1>
                    <p className="text-muted-foreground">
                        Manage all your products, view details, and track inventory levels.
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                    <Button variant="outline" size="sm">
                        <Upload className="h-4 w-4 mr-2" />
                        Import
                    </Button>
                    <Button size="sm" onClick={() => navigate("/inventory/products/add")}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Product
                    </Button>
                </div>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search products by name, SKU, or category..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <div className="flex gap-2">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                <Filter className="h-4 w-4 mr-2" />
                                Status
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                                All Status
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setFilterStatus("active")}>
                                Active
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterStatus("low-stock")}>
                                Low Stock
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterStatus("out-of-stock")}>
                                Out of Stock
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                Category
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setFilterCategory("all")}>
                                All Categories
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {categories.map(category => (
                                <DropdownMenuItem key={category} onClick={() => setFilterCategory(category)}>
                                    {category}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                {sortOrder === "asc" ? <SortAsc className="h-4 w-4 mr-2" /> : <SortDesc className="h-4 w-4 mr-2" />}
                                Sort
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => { setSortBy("name"); setSortOrder("asc") }}>
                                Name (A-Z)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("name"); setSortOrder("desc") }}>
                                Name (Z-A)
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => { setSortBy("stock"); setSortOrder("desc") }}>
                                Stock (High-Low)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("stock"); setSortOrder("asc") }}>
                                Stock (Low-High)
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => { setSortBy("price"); setSortOrder("desc") }}>
                                Price (High-Low)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("price"); setSortOrder("asc") }}>
                                Price (Low-High)
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Products Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Products ({filteredProducts.length})</span>
                        <div className="flex gap-2">
                            <Badge variant="secondary">
                                Active: {products.filter(p => p.status === "Active").length}
                            </Badge>
                            <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
                                Low Stock: {products.filter(p => p.stock <= p.lowStockThreshold && p.stock > 0).length}
                            </Badge>
                            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                                Out of Stock: {products.filter(p => p.stock === 0).length}
                            </Badge>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredProducts.length === 0 ? (
                        <div className="text-center py-8">
                            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No products found</h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm || filterStatus !== "all" || filterCategory !== "all"
                                    ? "Try adjusting your search or filter criteria."
                                    : "Start by adding your first product to the inventory."
                                }
                            </p>
                            <Button onClick={() => navigate("/inventory/products/add")}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Product
                            </Button>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Product</TableHead>
                                        <TableHead>SKU</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Unit</TableHead>
                                        <TableHead>Stock</TableHead>
                                        <TableHead>Price</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Last Updated</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredProducts.map((product) => (
                                        <TableRow key={product.id}>
                                            <TableCell>
                                                <div className="font-medium">{product.name}</div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                                            <TableCell className="text-muted-foreground">{product.category}</TableCell>
                                            <TableCell className="text-muted-foreground">{product.unit}</TableCell>
                                            <TableCell>
                                                <span className={`font-medium ${
                                                    product.stock === 0 ? 'text-red-600' :
                                                    product.stock <= product.lowStockThreshold ? 'text-orange-600' : 'text-green-600'
                                                }`}>
                                                    {product.stock}
                                                </span>
                                            </TableCell>
                                            <TableCell>₹{product.price}</TableCell>
                                            <TableCell>{getStatusBadge(product)}</TableCell>
                                            <TableCell className="text-muted-foreground text-sm">{product.lastUpdated}</TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => handleProductAction('view', product.id)}>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleProductAction('edit', product.id)}>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit Product
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem
                                                            onClick={() => handleProductAction('delete', product.id)}
                                                            className="text-red-600 focus:text-red-600"
                                                        >
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Delete Confirmation Dialog */}
            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Product</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this product? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteProduct}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
